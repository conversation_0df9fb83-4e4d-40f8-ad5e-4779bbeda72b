"""
Global Model Registry

This module provides a global registry for model classes that can be accessed
across the application. It's used by the Environment to resolve model names
to model classes during XML data loading and other operations.
"""
from typing import Dict, Optional, Type, Set
from ..logging import get_logger

logger = get_logger(__name__)


class GlobalModelRegistry:
    """
    Global registry for model classes.
    
    This registry maintains a mapping of model names to model classes
    across all loaded addons. It's used by the Environment to resolve
    model names during operations like XML data loading.
    """
    
    def __init__(self):
        self._models: Dict[str, Type] = {}
        self._addon_models: Dict[str, Set[str]] = {}  # addon_name -> set of model names
        self._logger = get_logger(__name__)
    
    def register_model(self, model_name: str, model_class: Type, addon_name: str = None) -> None:
        """
        Register a model class in the global registry.
        
        Args:
            model_name: Technical name of the model (e.g., 'ir.model.data')
            model_class: The model class
            addon_name: Name of the addon that provides this model
        """
        self._models[model_name] = model_class
        
        if addon_name:
            if addon_name not in self._addon_models:
                self._addon_models[addon_name] = set()
            self._addon_models[addon_name].add(model_name)
        
        self._logger.debug(f"Registered model {model_name} from addon {addon_name}")
    
    def unregister_model(self, model_name: str) -> None:
        """
        Unregister a model from the global registry.
        
        Args:
            model_name: Technical name of the model to unregister
        """
        if model_name in self._models:
            del self._models[model_name]
            
            # Remove from addon models tracking
            for model_names in self._addon_models.values():
                if model_name in model_names:
                    model_names.remove(model_name)
                    break
            
            self._logger.debug(f"Unregistered model {model_name}")
    
    def unregister_addon_models(self, addon_name: str) -> None:
        """
        Unregister all models from a specific addon.
        
        Args:
            addon_name: Name of the addon whose models to unregister
        """
        if addon_name in self._addon_models:
            model_names = self._addon_models[addon_name].copy()
            for model_name in model_names:
                self.unregister_model(model_name)
            
            del self._addon_models[addon_name]
            self._logger.debug(f"Unregistered all models from addon {addon_name}")
    
    def get_model(self, model_name: str) -> Optional[Type]:
        """
        Get a model class by name.
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            Model class or None if not found
        """
        return self._models.get(model_name)
    
    def has_model(self, model_name: str) -> bool:
        """
        Check if a model is registered.
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            True if model is registered, False otherwise
        """
        return model_name in self._models
    
    def get_all_models(self) -> Dict[str, Type]:
        """
        Get all registered models.
        
        Returns:
            Dictionary mapping model names to model classes
        """
        return self._models.copy()
    
    def get_addon_models(self, addon_name: str) -> Set[str]:
        """
        Get all model names for a specific addon.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            Set of model names for the addon
        """
        return self._addon_models.get(addon_name, set()).copy()
    
    def clear(self) -> None:
        """Clear all registered models."""
        self._models.clear()
        self._addon_models.clear()
        self._logger.debug("Cleared all registered models")
    
    def get_model_count(self) -> int:
        """Get the number of registered models."""
        return len(self._models)
    
    def register_addon_models(self, addon_name: str, models: Dict[str, Type]) -> None:
        """
        Register multiple models from an addon at once.
        
        Args:
            addon_name: Name of the addon
            models: Dictionary mapping model names to model classes
        """
        for model_name, model_class in models.items():
            self.register_model(model_name, model_class, addon_name)
        
        self._logger.debug(f"Registered {len(models)} models from addon {addon_name}")


# Global registry instance
_global_model_registry = GlobalModelRegistry()


def get_model_registry() -> GlobalModelRegistry:
    """Get the global model registry instance."""
    return _global_model_registry


def register_model(model_name: str, model_class: Type, addon_name: str = None) -> None:
    """
    Convenience function to register a model in the global registry.
    
    Args:
        model_name: Technical name of the model
        model_class: The model class
        addon_name: Name of the addon that provides this model
    """
    _global_model_registry.register_model(model_name, model_class, addon_name)


def get_model(model_name: str) -> Optional[Type]:
    """
    Convenience function to get a model from the global registry.
    
    Args:
        model_name: Technical name of the model
        
    Returns:
        Model class or None if not found
    """
    return _global_model_registry.get_model(model_name)
